-- Row Level Security Policies for NAHPi Complaints
-- Run this after creating the schema

-- Helper function to get user role
CREATE OR REPLACE FUNCTION get_user_role(user_uuid UUID)
RETURNS TEXT AS $$
BEGIN
    RETURN (SELECT role FROM public.users WHERE id = user_uuid);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Users table policies
CREATE POLICY "Users can view their own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Admins can view all users" ON public.users
    FOR SELECT USING (get_user_role(auth.uid()) = 'admin');

CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Ad<PERSON> can update all users" ON public.users
    FOR UPDATE USING (get_user_role(auth.uid()) = 'admin');

CREATE POLICY "Anyone can insert users during registration" ON public.users
    FOR INSERT WITH CHECK (true);

-- Students table policies
CREATE POLICY "Students can view their own data" ON public.students
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Admins can view all students" ON public.students
    FOR SELECT USING (get_user_role(auth.uid()) = 'admin');

CREATE POLICY "Students can update their own data" ON public.students
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Admins can update all students" ON public.students
    FOR UPDATE USING (get_user_role(auth.uid()) = 'admin');

CREATE POLICY "Anyone can insert students during registration" ON public.students
    FOR INSERT WITH CHECK (true);

-- Departments table policies
CREATE POLICY "Everyone can view active departments" ON public.departments
    FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage departments" ON public.departments
    FOR ALL USING (get_user_role(auth.uid()) = 'admin');

-- Department Officers table policies
CREATE POLICY "Department officers can view their own data" ON public.department_officers
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Admins can view all department officers" ON public.department_officers
    FOR SELECT USING (get_user_role(auth.uid()) = 'admin');

CREATE POLICY "Admins can manage department officers" ON public.department_officers
    FOR ALL USING (get_user_role(auth.uid()) = 'admin');

-- Complaints table policies
CREATE POLICY "Students can view their own complaints" ON public.complaints
    FOR SELECT USING (
        student_id IN (SELECT id FROM public.students WHERE user_id = auth.uid())
    );

CREATE POLICY "Department officers can view their department complaints" ON public.complaints
    FOR SELECT USING (
        department_id IN (
            SELECT department_id FROM public.department_officers WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Admins can view all complaints" ON public.complaints
    FOR SELECT USING (get_user_role(auth.uid()) = 'admin');

CREATE POLICY "Students can insert their own complaints" ON public.complaints
    FOR INSERT WITH CHECK (
        student_id IN (SELECT id FROM public.students WHERE user_id = auth.uid())
    );

CREATE POLICY "Department officers can update their department complaints" ON public.complaints
    FOR UPDATE USING (
        department_id IN (
            SELECT department_id FROM public.department_officers WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Admins can update all complaints" ON public.complaints
    FOR UPDATE USING (get_user_role(auth.uid()) = 'admin');

-- Complaint Attachments policies
CREATE POLICY "Users can view attachments for complaints they can access" ON public.complaint_attachments
    FOR SELECT USING (
        complaint_id IN (
            SELECT id FROM public.complaints WHERE
                student_id IN (SELECT id FROM public.students WHERE user_id = auth.uid()) OR
                department_id IN (SELECT department_id FROM public.department_officers WHERE user_id = auth.uid()) OR
                get_user_role(auth.uid()) = 'admin'
        )
    );

CREATE POLICY "Students can insert attachments for their complaints" ON public.complaint_attachments
    FOR INSERT WITH CHECK (
        complaint_id IN (
            SELECT id FROM public.complaints WHERE
                student_id IN (SELECT id FROM public.students WHERE user_id = auth.uid())
        )
    );

-- Complaint Responses policies
CREATE POLICY "Users can view responses for complaints they can access" ON public.complaint_responses
    FOR SELECT USING (
        complaint_id IN (
            SELECT id FROM public.complaints WHERE
                student_id IN (SELECT id FROM public.students WHERE user_id = auth.uid()) OR
                department_id IN (SELECT department_id FROM public.department_officers WHERE user_id = auth.uid()) OR
                get_user_role(auth.uid()) = 'admin'
        )
    );

CREATE POLICY "Department officers and admins can insert responses" ON public.complaint_responses
    FOR INSERT WITH CHECK (
        get_user_role(auth.uid()) IN ('department_officer', 'admin') AND
        complaint_id IN (
            SELECT id FROM public.complaints WHERE
                department_id IN (SELECT department_id FROM public.department_officers WHERE user_id = auth.uid()) OR
                get_user_role(auth.uid()) = 'admin'
        )
    );

-- Complaint Feedback policies
CREATE POLICY "Students can view feedback for their complaints" ON public.complaint_feedback
    FOR SELECT USING (
        complaint_id IN (
            SELECT id FROM public.complaints WHERE
                student_id IN (SELECT id FROM public.students WHERE user_id = auth.uid())
        )
    );

CREATE POLICY "Students can insert feedback for their resolved complaints" ON public.complaint_feedback
    FOR INSERT WITH CHECK (
        complaint_id IN (
            SELECT id FROM public.complaints WHERE
                student_id IN (SELECT id FROM public.students WHERE user_id = auth.uid()) AND
                status = 'resolved'
        )
    );

CREATE POLICY "Admins can view all feedback" ON public.complaint_feedback
    FOR SELECT USING (get_user_role(auth.uid()) = 'admin');

-- Notifications policies
CREATE POLICY "Users can view their own notifications" ON public.notifications
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update their own notifications" ON public.notifications
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "System can insert notifications" ON public.notifications
    FOR INSERT WITH CHECK (true);

-- Verification Codes policies
CREATE POLICY "Users can view their own verification codes" ON public.verification_codes
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "System can manage verification codes" ON public.verification_codes
    FOR ALL WITH CHECK (true);

-- System Settings policies
CREATE POLICY "Everyone can view system settings" ON public.system_settings
    FOR SELECT USING (true);

CREATE POLICY "Admins can manage system settings" ON public.system_settings
    FOR ALL USING (get_user_role(auth.uid()) = 'admin');

-- Create function to generate complaint ID
CREATE OR REPLACE FUNCTION generate_complaint_id()
RETURNS TEXT AS $$
DECLARE
    new_id TEXT;
    year_suffix TEXT;
BEGIN
    year_suffix := EXTRACT(YEAR FROM NOW())::TEXT;
    SELECT 'CMP-' || year_suffix || '-' || LPAD((COUNT(*) + 1)::TEXT, 4, '0')
    INTO new_id
    FROM public.complaints
    WHERE EXTRACT(YEAR FROM submitted_at) = EXTRACT(YEAR FROM NOW());
    
    RETURN new_id;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-generate complaint ID
CREATE OR REPLACE FUNCTION set_complaint_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.complaint_id IS NULL OR NEW.complaint_id = '' THEN
        NEW.complaint_id := generate_complaint_id();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_set_complaint_id
    BEFORE INSERT ON public.complaints
    FOR EACH ROW
    EXECUTE FUNCTION set_complaint_id();

-- Create function to notify department officers
CREATE OR REPLACE FUNCTION notify_department_officer()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.notifications (user_id, type, title, message, related_complaint_id)
    SELECT 
        do.user_id,
        'new_complaint',
        'New Complaint Assigned',
        'A new complaint has been submitted to your department: ' || NEW.title,
        NEW.id
    FROM public.department_officers do
    WHERE do.department_id = NEW.department_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_notify_department_officer
    AFTER INSERT ON public.complaints
    FOR EACH ROW
    EXECUTE FUNCTION notify_department_officer();

-- Create function to notify student on status change
CREATE OR REPLACE FUNCTION notify_student_status_change()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.status != NEW.status THEN
        INSERT INTO public.notifications (user_id, type, title, message, related_complaint_id)
        SELECT 
            s.user_id,
            'status_update',
            'Complaint Status Updated',
            'Your complaint "' || NEW.title || '" status has been updated to: ' || NEW.status,
            NEW.id
        FROM public.students s
        WHERE s.id = NEW.student_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_notify_student_status_change
    AFTER UPDATE ON public.complaints
    FOR EACH ROW
    EXECUTE FUNCTION notify_student_status_change();
