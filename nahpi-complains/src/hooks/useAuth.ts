'use client'

import { useState, useEffect, createContext, useContext } from 'react'
import { User } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'
import { clientAuthService, AuthUser } from '@/lib/auth-client'

interface AuthContextType {
  user: AuthUser | null
  profile: any | null
  loading: boolean
  signIn: (email: string, password: string, role?: 'admin' | 'department_officer') => Promise<any>
  signInStudent: (matricule: string, password: string) => Promise<any>
  signUp: (data: any) => Promise<any>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<any>
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export function useAuthProvider() {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [profile, setProfile] = useState<any | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const { user: currentUser, profile: currentProfile } = await clientAuthService.getCurrentUser()
      setUser(currentUser)
      setProfile(currentProfile)
      setLoading(false)
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (session?.user) {
          const { user: currentUser, profile: currentProfile } = await clientAuthService.getCurrentUser()
          setUser(currentUser)
          setProfile(currentProfile)
        } else {
          setUser(null)
          setProfile(null)
        }
        setLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const signIn = async (email: string, password: string, role: 'admin' | 'department_officer' = 'admin') => {
    setLoading(true)
    try {
      const result = await clientAuthService.signInUser(email, password, role)
      return result
    } finally {
      setLoading(false)
    }
  }

  const signInStudent = async (matricule: string, password: string) => {
    setLoading(true)
    try {
      const result = await clientAuthService.signInStudent(matricule, password)
      return result
    } finally {
      setLoading(false)
    }
  }

  const signUp = async (data: any) => {
    setLoading(true)
    try {
      const result = await clientAuthService.signUpStudent(data)
      return result
    } finally {
      setLoading(false)
    }
  }

  const signOut = async () => {
    setLoading(true)
    try {
      await clientAuthService.signOut()
      setUser(null)
      setProfile(null)
    } finally {
      setLoading(false)
    }
  }

  const resetPassword = async (email: string) => {
    return await clientAuthService.resetPassword(email)
  }

  return {
    user,
    profile,
    loading,
    signIn,
    signInStudent,
    signUp,
    signOut,
    resetPassword
  }
}

export { AuthContext }
