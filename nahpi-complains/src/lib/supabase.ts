import { createClient } from '@supabase/supabase-js'
import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Client-side Supabase client
export const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey)

// Server-side Supabase client (for API routes)
export const supabaseAdmin = createClient(
  supabaseUrl,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

// Database types
export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          name: string
          role: 'student' | 'admin' | 'department_officer'
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          name: string
          role: 'student' | 'admin' | 'department_officer'
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          name?: string
          role?: 'student' | 'admin' | 'department_officer'
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      students: {
        Row: {
          id: string
          user_id: string
          matricule: string
          department: string
          year_of_study: number
          phone_number: string
          academic_year: string
          verification_method: 'email' | 'phone'
          is_verified: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          matricule: string
          department: string
          year_of_study: number
          phone_number: string
          academic_year: string
          verification_method?: 'email' | 'phone'
          is_verified?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          matricule?: string
          department?: string
          year_of_study?: number
          phone_number?: string
          academic_year?: string
          verification_method?: 'email' | 'phone'
          is_verified?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      departments: {
        Row: {
          id: string
          name: string
          code: string
          description: string | null
          head_of_department: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          code: string
          description?: string | null
          head_of_department?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          code?: string
          description?: string | null
          head_of_department?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      department_officers: {
        Row: {
          id: string
          user_id: string
          department_id: string
          position: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          department_id: string
          position: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          department_id?: string
          position?: string
          created_at?: string
          updated_at?: string
        }
      }
      complaints: {
        Row: {
          id: string
          complaint_id: string
          student_id: string
          title: string
          description: string
          category: 'ca_mark' | 'exam_mark' | 'other'
          status: 'pending' | 'in_progress' | 'resolved' | 'rejected'
          priority: 'low' | 'medium' | 'high'
          course_code: string
          course_title: string
          course_level: string
          semester: string
          academic_year: string
          assigned_to: string | null
          department_id: string
          submitted_at: string
          updated_at: string
          resolved_at: string | null
        }
        Insert: {
          id?: string
          complaint_id: string
          student_id: string
          title: string
          description: string
          category: 'ca_mark' | 'exam_mark' | 'other'
          status?: 'pending' | 'in_progress' | 'resolved' | 'rejected'
          priority?: 'low' | 'medium' | 'high'
          course_code: string
          course_title: string
          course_level: string
          semester: string
          academic_year: string
          assigned_to?: string | null
          department_id: string
          submitted_at?: string
          updated_at?: string
          resolved_at?: string | null
        }
        Update: {
          id?: string
          complaint_id?: string
          student_id?: string
          title?: string
          description?: string
          category?: 'ca_mark' | 'exam_mark' | 'other'
          status?: 'pending' | 'in_progress' | 'resolved' | 'rejected'
          priority?: 'low' | 'medium' | 'high'
          course_code?: string
          course_title?: string
          course_level?: string
          semester?: string
          academic_year?: string
          assigned_to?: string | null
          department_id?: string
          submitted_at?: string
          updated_at?: string
          resolved_at?: string | null
        }
      }
    }
  }
}
