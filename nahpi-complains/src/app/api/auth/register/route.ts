import { NextRequest, NextResponse } from 'next/server'
import { authService } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      email,
      password,
      name,
      matricule,
      department,
      year_of_study,
      phone_number,
      academic_year,
      verification_method
    } = body

    // Validate required fields
    if (!email || !password || !name || !matricule || !department || !year_of_study || !phone_number || !academic_year) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json({ error: 'Invalid email format' }, { status: 400 })
    }

    // Validate password strength
    if (password.length < 8) {
      return NextResponse.json({ error: 'Password must be at least 8 characters long' }, { status: 400 })
    }

    // Validate matricule format (UBa<YY><Letter><4Digits>)
    const matriculeRegex = /^UBa\d{2}[A-Z]\d{4}$/
    if (!matriculeRegex.test(matricule)) {
      return NextResponse.json({
        error: 'Invalid matricule format. Expected format: UBa25E1000'
      }, { status: 400 })
    }

    // Validate year of study
    if (year_of_study < 1 || year_of_study > 7) {
      return NextResponse.json({ error: 'Year of study must be between 1 and 7' }, { status: 400 })
    }

    // Validate phone number format
    const phoneRegex = /^\+?[1-9]\d{1,14}$/
    if (!phoneRegex.test(phone_number.replace(/\s/g, ''))) {
      return NextResponse.json({ error: 'Invalid phone number format' }, { status: 400 })
    }

    const result = await authService.signUpStudent({
      email,
      password,
      name,
      matricule,
      department,
      year_of_study: parseInt(year_of_study),
      phone_number,
      academic_year,
      verification_method: verification_method || 'email'
    })

    if (result.error) {
      console.error('Registration error:', result.error)
      
      // Handle specific Supabase errors
      if (result.error.message?.includes('duplicate key')) {
        if (result.error.message.includes('email')) {
          return NextResponse.json({ error: 'Email already registered' }, { status: 409 })
        }
        if (result.error.message.includes('matricule')) {
          return NextResponse.json({ error: 'Matricule already registered' }, { status: 409 })
        }
      }
      
      return NextResponse.json({ 
        error: result.error.message || 'Registration failed' 
      }, { status: 400 })
    }

    return NextResponse.json({
      message: 'Registration successful. Please check your email/phone for verification code.',
      user: result.data?.user
    }, { status: 201 })

  } catch (error) {
    console.error('Error in POST /api/auth/register:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
