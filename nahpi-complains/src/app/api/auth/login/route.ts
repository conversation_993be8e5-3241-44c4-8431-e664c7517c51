import { NextRequest, NextResponse } from 'next/server'
import { authService } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { identifier, password, role } = body

    // Validate required fields
    if (!identifier || !password) {
      return NextResponse.json({ error: 'Missing credentials' }, { status: 400 })
    }

    let result

    if (role === 'student') {
      // Student login with matricule
      result = await authService.signInStudent(identifier, password)
    } else if (role === 'admin' || role === 'department_officer') {
      // Admin/Department officer login with email
      result = await authService.signInUser(identifier, password, role)
    } else {
      return NextResponse.json({ error: 'Invalid role specified' }, { status: 400 })
    }

    if (result.error) {
      console.error('Login error:', result.error)
      
      // Handle specific errors
      if (result.error.message?.includes('Invalid login credentials')) {
        return NextResponse.json({ error: 'Invalid credentials' }, { status: 401 })
      }
      if (result.error.message?.includes('Email not confirmed')) {
        return NextResponse.json({ error: 'Please verify your email first' }, { status: 401 })
      }
      if (result.error.message?.includes('deactivated')) {
        return NextResponse.json({ error: 'Account is deactivated' }, { status: 403 })
      }
      
      return NextResponse.json({ 
        error: result.error.message || 'Login failed' 
      }, { status: 401 })
    }

    return NextResponse.json({
      message: 'Login successful',
      user: result.data?.user,
      session: result.data?.session
    })

  } catch (error) {
    console.error('Error in POST /api/auth/login:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
