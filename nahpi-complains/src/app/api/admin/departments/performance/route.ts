import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase-admin'
import { serverAuthService } from '@/lib/auth-server'

// GET /api/admin/departments/performance - Get department performance statistics
export async function GET(request: NextRequest) {
  try {
    const { user } = await serverAuthService.getCurrentUserFromRequest(request)
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get all departments with their complaints and officers
    const { data: departments, error: deptError } = await supabaseAdmin
      .from('departments')
      .select(`
        id,
        name,
        code,
        is_active,
        complaints:complaints(id, status, submitted_at, resolved_at),
        officers:department_officers(id, user:users(name, email))
      `)
      .eq('is_active', true)

    if (deptError) {
      console.error('Error fetching departments:', deptError)
      return NextResponse.json({ error: 'Failed to fetch department data' }, { status: 500 })
    }

    // Calculate performance metrics for each department
    const departmentStats = departments.map(dept => {
      const complaints = dept.complaints || []
      const officers = dept.officers || []

      const totalComplaints = complaints.length
      const resolvedComplaints = complaints.filter(c => c.status === 'resolved').length
      const pendingComplaints = complaints.filter(c => c.status === 'pending').length
      const inProgressComplaints = complaints.filter(c => c.status === 'in_progress').length
      const rejectedComplaints = complaints.filter(c => c.status === 'rejected').length

      // Calculate overdue complaints (pending for more than 7 days)
      const sevenDaysAgo = new Date()
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
      const overdueComplaints = complaints.filter(c => 
        c.status === 'pending' && new Date(c.submitted_at) < sevenDaysAgo
      ).length

      // Calculate average resolution time
      const resolvedWithTime = complaints.filter(c => c.status === 'resolved' && c.resolved_at)
      const averageResolutionTime = resolvedWithTime.length > 0 
        ? resolvedWithTime.reduce((acc, c) => {
            const submitted = new Date(c.submitted_at)
            const resolved = new Date(c.resolved_at)
            const days = (resolved.getTime() - submitted.getTime()) / (1000 * 60 * 60 * 24)
            return acc + days
          }, 0) / resolvedWithTime.length
        : 0

      // Calculate resolution rate
      const resolutionRate = totalComplaints > 0 ? (resolvedComplaints / totalComplaints) * 100 : 0

      return {
        id: dept.id,
        name: dept.name,
        code: dept.code,
        totalComplaints,
        resolvedComplaints,
        pendingComplaints,
        inProgressComplaints,
        rejectedComplaints,
        overdueComplaints,
        totalOfficers: officers.length,
        averageResolutionTime: Math.round(averageResolutionTime * 10) / 10,
        resolutionRate: Math.round(resolutionRate * 10) / 10,
        officers: officers.map(o => ({
          id: o.id,
          name: o.user?.name,
          email: o.user?.email
        }))
      }
    })

    // Sort by resolution rate (best performing first)
    departmentStats.sort((a, b) => b.resolutionRate - a.resolutionRate)

    return NextResponse.json({ departments: departmentStats })
  } catch (error) {
    console.error('Error in GET /api/admin/departments/performance:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
