import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase-admin'
import { authService } from '@/lib/auth'

// GET /api/admin/users/[id] - Get specific user
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user } = await authService.getCurrentUser()
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: userData, error } = await supabaseAdmin
      .from('users')
      .select(`
        *,
        student:students(matricule, department, year_of_study, phone_number, academic_year, is_verified),
        department_officer:department_officers(
          position,
          department:departments(id, name, code)
        )
      `)
      .eq('id', params.id)
      .single()

    if (error) {
      console.error('Error fetching user:', error)
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    return NextResponse.json({ user: userData })
  } catch (error) {
    console.error('Error in GET /api/admin/users/[id]:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PATCH /api/admin/users/[id] - Update user
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user } = await authService.getCurrentUser()
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { name, is_active, department_id, position } = body

    // Update user profile
    const updateData: any = {}
    if (name !== undefined) updateData.name = name
    if (is_active !== undefined) updateData.is_active = is_active

    if (Object.keys(updateData).length > 0) {
      const { error: userError } = await supabaseAdmin
        .from('users')
        .update(updateData)
        .eq('id', params.id)

      if (userError) {
        console.error('Error updating user:', userError)
        return NextResponse.json({ error: 'Failed to update user' }, { status: 500 })
      }
    }

    // Update department officer profile if applicable
    if (department_id !== undefined || position !== undefined) {
      const officerUpdateData: any = {}
      if (department_id !== undefined) officerUpdateData.department_id = department_id
      if (position !== undefined) officerUpdateData.position = position

      const { error: officerError } = await supabaseAdmin
        .from('department_officers')
        .update(officerUpdateData)
        .eq('user_id', params.id)

      if (officerError) {
        console.error('Error updating department officer:', officerError)
        return NextResponse.json({ error: 'Failed to update department officer' }, { status: 500 })
      }
    }

    return NextResponse.json({ message: 'User updated successfully' })
  } catch (error) {
    console.error('Error in PATCH /api/admin/users/[id]:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// DELETE /api/admin/users/[id] - Delete user
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user } = await authService.getCurrentUser()
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user to check role
    const { data: userData, error: userError } = await supabaseAdmin
      .from('users')
      .select('role')
      .eq('id', params.id)
      .single()

    if (userError) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Delete role-specific profiles first
    if (userData.role === 'student') {
      await supabaseAdmin.from('students').delete().eq('user_id', params.id)
    } else if (userData.role === 'department_officer') {
      await supabaseAdmin.from('department_officers').delete().eq('user_id', params.id)
    }

    // Delete user profile
    const { error: deleteError } = await supabaseAdmin
      .from('users')
      .delete()
      .eq('id', params.id)

    if (deleteError) {
      console.error('Error deleting user:', deleteError)
      return NextResponse.json({ error: 'Failed to delete user' }, { status: 500 })
    }

    // Delete auth user
    const { error: authDeleteError } = await supabaseAdmin.auth.admin.deleteUser(params.id)
    if (authDeleteError) {
      console.error('Error deleting auth user:', authDeleteError)
      // User profile is already deleted, so we'll log this but not fail the request
    }

    return NextResponse.json({ message: 'User deleted successfully' })
  } catch (error) {
    console.error('Error in DELETE /api/admin/users/[id]:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
