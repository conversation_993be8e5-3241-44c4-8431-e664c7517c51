import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase-admin'
import { authService } from '@/lib/auth'

// GET /api/complaints - Get complaints based on user role
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const department = searchParams.get('department')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = (page - 1) * limit

    // Get current user
    const { user, profile } = await authService.getCurrentUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    let query = supabaseAdmin
      .from('complaints')
      .select(`
        *,
        student:students(
          matricule,
          department,
          user:users(name, email)
        ),
        department:departments(name, code),
        assigned_officer:department_officers(
          user:users(name, email)
        )
      `)

    // Apply filters based on user role
    if (user.role === 'student') {
      query = query.eq('student_id', profile?.id)
    } else if (user.role === 'department_officer') {
      query = query.eq('department_id', profile?.department_id)
    }
    // Admins can see all complaints (no additional filter)

    // Apply additional filters
    if (status) {
      query = query.eq('status', status)
    }
    if (department && user.role === 'admin') {
      query = query.eq('department_id', department)
    }

    // Apply pagination
    query = query
      .order('submitted_at', { ascending: false })
      .range(offset, offset + limit - 1)

    const { data, error, count } = await query

    if (error) {
      console.error('Error fetching complaints:', error)
      return NextResponse.json({ error: 'Failed to fetch complaints' }, { status: 500 })
    }

    return NextResponse.json({
      complaints: data,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    })
  } catch (error) {
    console.error('Error in GET /api/complaints:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/complaints - Create new complaint
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      title,
      description,
      category,
      course_code,
      course_title,
      course_level,
      semester,
      academic_year,
      department_id,
      priority = 'medium'
    } = body

    // Get current user
    const { user, profile } = await authService.getCurrentUser()
    if (!user || user.role !== 'student') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    if (!profile?.is_verified) {
      return NextResponse.json({ error: 'Account not verified' }, { status: 403 })
    }

    // Validate required fields
    if (!title || !description || !category || !course_code || !course_title || !department_id) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Create complaint
    const { data, error } = await supabaseAdmin
      .from('complaints')
      .insert({
        student_id: profile.id,
        title,
        description,
        category,
        course_code,
        course_title,
        course_level,
        semester,
        academic_year,
        department_id,
        priority,
        status: 'pending'
      })
      .select(`
        *,
        student:students(
          matricule,
          department,
          user:users(name, email)
        ),
        department:departments(name, code)
      `)
      .single()

    if (error) {
      console.error('Error creating complaint:', error)
      return NextResponse.json({ error: 'Failed to create complaint' }, { status: 500 })
    }

    return NextResponse.json({ complaint: data }, { status: 201 })
  } catch (error) {
    console.error('Error in POST /api/complaints:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
