import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { authService } from '@/lib/auth'

// GET /api/complaints/[id] - Get specific complaint
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user, profile } = await authService.getCurrentUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    let query = supabase
      .from('complaints')
      .select(`
        *,
        student:students(
          matricule,
          department,
          user:users(name, email)
        ),
        department:departments(name, code),
        assigned_officer:department_officers(
          user:users(name, email)
        ),
        attachments:complaint_attachments(*),
        responses:complaint_responses(
          *,
          responder:users(name, email)
        ),
        feedback:complaint_feedback(*)
      `)
      .eq('id', params.id)

    // Apply access control
    if (user.role === 'student') {
      query = query.eq('student_id', profile?.id)
    } else if (user.role === 'department_officer') {
      query = query.eq('department_id', profile?.department_id)
    }

    const { data, error } = await query.single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Complaint not found' }, { status: 404 })
      }
      console.error('Error fetching complaint:', error)
      return NextResponse.json({ error: 'Failed to fetch complaint' }, { status: 500 })
    }

    return NextResponse.json({ complaint: data })
  } catch (error) {
    console.error('Error in GET /api/complaints/[id]:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PATCH /api/complaints/[id] - Update complaint status/assignment
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const { status, assigned_to, priority, response_message } = body

    const { user, profile } = await authService.getCurrentUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only department officers and admins can update complaints
    if (user.role === 'student') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Get the complaint first to check permissions
    let complaintQuery = supabase
      .from('complaints')
      .select('*')
      .eq('id', params.id)

    if (user.role === 'department_officer') {
      complaintQuery = complaintQuery.eq('department_id', profile?.department_id)
    }

    const { data: complaint, error: fetchError } = await complaintQuery.single()

    if (fetchError) {
      return NextResponse.json({ error: 'Complaint not found' }, { status: 404 })
    }

    // Prepare update data
    const updateData: any = {}
    if (status) updateData.status = status
    if (assigned_to) updateData.assigned_to = assigned_to
    if (priority) updateData.priority = priority
    if (status === 'resolved') updateData.resolved_at = new Date().toISOString()

    // Update complaint
    const { data, error } = await supabase
      .from('complaints')
      .update(updateData)
      .eq('id', params.id)
      .select(`
        *,
        student:students(
          matricule,
          department,
          user:users(name, email)
        ),
        department:departments(name, code)
      `)
      .single()

    if (error) {
      console.error('Error updating complaint:', error)
      return NextResponse.json({ error: 'Failed to update complaint' }, { status: 500 })
    }

    // Add response message if provided
    if (response_message) {
      await supabase
        .from('complaint_responses')
        .insert({
          complaint_id: params.id,
          responder_id: user.id,
          message: response_message,
          is_internal: false
        })
    }

    return NextResponse.json({ complaint: data })
  } catch (error) {
    console.error('Error in PATCH /api/complaints/[id]:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// DELETE /api/complaints/[id] - Delete complaint (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user } = await authService.getCurrentUser()
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { error } = await supabase
      .from('complaints')
      .delete()
      .eq('id', params.id)

    if (error) {
      console.error('Error deleting complaint:', error)
      return NextResponse.json({ error: 'Failed to delete complaint' }, { status: 500 })
    }

    return NextResponse.json({ message: 'Complaint deleted successfully' })
  } catch (error) {
    console.error('Error in DELETE /api/complaints/[id]:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
