'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { useAuth } from '@/hooks/useAuth'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase'

interface AdminStats {
  totalComplaints: number
  pendingComplaints: number
  inProgressComplaints: number
  resolvedComplaints: number
  rejectedComplaints: number
  overdueComplaints: number
  totalUsers: number
  totalStudents: number
  totalOfficers: number
  totalDepartments: number
  activeUsers: number
  averageResolutionTime: number
}

interface RecentComplaint {
  id: string
  title: string
  status: string
  priority: string
  submitted_at: string
  student: {
    user: {
      name: string
    }
  }
  department: {
    name: string
  }
}

interface DepartmentPerformance {
  id: string
  name: string
  totalComplaints: number
  resolvedComplaints: number
  pendingComplaints: number
  totalOfficers: number
}

export default function AdminDashboard() {
  const router = useRouter()
  const { user, loading: authLoading } = useAuth()
  const [stats, setStats] = useState<AdminStats | null>(null)
  const [recentComplaints, setRecentComplaints] = useState<RecentComplaint[]>([])
  const [departmentStats, setDepartmentStats] = useState<DepartmentPerformance[]>([])
  const [loading, setLoading] = useState(true)

  // Redirect if not authenticated or not admin
  useEffect(() => {
    if (!authLoading && (!user || user.role !== 'admin')) {
      router.push('/admin/login')
    }
  }, [user, authLoading, router])

  // Load dashboard data when user is authenticated
  useEffect(() => {
    if (user && user.role === 'admin') {
      loadDashboardData()
    }
  }, [user])

  const loadDashboardData = async () => {
    try {
      setLoading(true)

      // Get the current session to access the access token
      const { data: { session } } = await supabase.auth.getSession()
      const headers = {
        'Authorization': `Bearer ${session?.access_token}`,
      }

      // Load admin stats
      const statsResponse = await fetch('/api/admin/stats', { headers })
      if (statsResponse.ok) {
        const { stats: adminStats } = await statsResponse.json()
        setStats(adminStats)
      }

      // Load recent complaints
      const complaintsResponse = await fetch('/api/complaints?limit=5', { headers })
      if (complaintsResponse.ok) {
        const { complaints } = await complaintsResponse.json()
        setRecentComplaints(complaints || [])
      }

      // Load department performance
      const deptResponse = await fetch('/api/admin/departments/performance', { headers })
      if (deptResponse.ok) {
        const { departments } = await deptResponse.json()
        setDepartmentStats(departments?.slice(0, 4) || [])
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading || !user || !stats) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  function getStatusColor(status: string) {
    switch (status) {
      case 'pending':
        return 'warning'
      case 'in_progress':
        return 'info'
      case 'resolved':
        return 'success'
      case 'rejected':
        return 'error'
      case 'unassigned':
        return 'secondary'
      default:
        return 'default'
    }
  }

  function getPriorityColor(priority: string) {
    switch (priority) {
      case 'high':
        return 'error'
      case 'medium':
        return 'warning'
      case 'low':
        return 'secondary'
      default:
        return 'default'
    }
  }

  return (
    <DashboardLayout user={user} notifications={15}>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600 mt-2">Overview of the complaint management system</p>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Total Complaints</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{stats.totalComplaints}</div>
              <p className="text-xs text-gray-500 mt-1">Total complaints</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">In Progress</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-info">{stats.inProgressComplaints}</div>
              <p className="text-xs text-gray-500 mt-1">Being processed</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Overdue</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-error">{stats.overdueComplaints}</div>
              <p className="text-xs text-gray-500 mt-1">Past deadline</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Avg Resolution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{stats.averageResolutionTime} days</div>
              <p className="text-xs text-gray-500 mt-1">Average time to resolve</p>
            </CardContent>
          </Card>
        </div>

        {/* Status Overview */}
        <div className="grid lg:grid-cols-3 gap-6">
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Complaint Status Overview</CardTitle>
              <CardDescription>Current distribution of complaint statuses</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-yellow-50 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">{stats.pendingComplaints}</div>
                  <div className="text-sm text-yellow-700">Pending</div>
                </div>
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{stats.inProgressComplaints}</div>
                  <div className="text-sm text-blue-700">In Progress</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{stats.resolvedComplaints}</div>
                  <div className="text-sm text-green-700">Resolved</div>
                </div>
                <div className="text-center p-4 bg-red-50 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">{stats.rejectedComplaints}</div>
                  <div className="text-sm text-red-700">Rejected</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>System Users</CardTitle>
              <CardDescription>User account statistics</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Total Users</span>
                <span className="font-semibold">{stats.totalUsers}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Students</span>
                <span className="font-semibold">{stats.totalStudents}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Department Officers</span>
                <span className="font-semibold">{stats.totalOfficers}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Departments</span>
                <span className="font-semibold">{stats.totalDepartments}</span>
              </div>
              <Link href="/admin/users">
                <Button variant="outline" size="sm" className="w-full mt-4">
                  Manage Users
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>

        {/* Recent Complaints & Department Performance */}
        <div className="grid lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Recent Complaints</CardTitle>
                  <CardDescription>Latest complaint submissions requiring attention</CardDescription>
                </div>
                <Link href="/admin/complaints">
                  <Button variant="outline" size="sm">View All</Button>
                </Link>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentComplaints.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    No recent complaints
                  </div>
                ) : (
                  recentComplaints.map((complaint) => (
                    <div key={complaint.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="font-medium text-sm text-gray-900 truncate">{complaint.title}</h4>
                        </div>
                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                          <span>Student: {complaint.student?.user?.name}</span>
                          <span>{formatDate(complaint.submitted_at)}</span>
                        </div>
                        <div className="flex items-center space-x-2 mt-2">
                          <Badge variant={getStatusColor(complaint.status)} size="sm">
                            {complaint.status.replace('_', ' ')}
                          </Badge>
                          <Badge variant={getPriorityColor(complaint.priority)} size="sm">
                            {complaint.priority}
                          </Badge>
                          <span className="text-xs text-gray-500">{complaint.department?.name}</span>
                        </div>
                      </div>
                      <Link href={`/admin/complaints/${complaint.id}`}>
                        <Button variant="ghost" size="sm">View</Button>
                      </Link>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Department Performance</CardTitle>
              <CardDescription>Complaint resolution by department</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {departmentStats.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    No department data available
                  </div>
                ) : (
                  departmentStats.map((dept) => (
                    <div key={dept.name} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-900">{dept.name}</span>
                        <span className="text-xs text-gray-500">{dept.totalOfficers} officers</span>
                      </div>
                      <div className="flex items-center space-x-2 text-xs">
                        <span className="text-gray-600">Total: {dept.totalComplaints}</span>
                        <span className="text-green-600">Resolved: {dept.resolvedComplaints}</span>
                        <span className="text-yellow-600">Pending: {dept.pendingComplaints}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-500 h-2 rounded-full"
                          style={{ width: `${dept.totalComplaints > 0 ? (dept.resolvedComplaints / dept.totalComplaints) * 100 : 0}%` }}
                        />
                      </div>
                    </div>
                  ))
                )}
              </div>
              <Link href="/admin/departments">
                <Button variant="outline" size="sm" className="w-full mt-4">
                  Manage Departments
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common administrative tasks</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Link href="/admin/complaints">
                <Button variant="outline" className="w-full h-20 flex-col">
                  <svg className="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  View Complaints
                </Button>
              </Link>
              
              <Link href="/admin/users/new">
                <Button variant="outline" className="w-full h-20 flex-col">
                  <svg className="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                  </svg>
                  Add User
                </Button>
              </Link>
              
              <Link href="/admin/departments/new">
                <Button variant="outline" className="w-full h-20 flex-col">
                  <svg className="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                  Add Department
                </Button>
              </Link>
              
              <Link href="/admin/reports">
                <Button variant="outline" className="w-full h-20 flex-col">
                  <svg className="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  Generate Report
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
